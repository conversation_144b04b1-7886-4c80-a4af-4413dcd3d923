APP_STL := c++_static

APP_CPPFLAGS := -frtti -DCC_ENABLE_CHIPMUNK_INTEGRATION=1 -std=c++11 -fsigned-char
APP_LDFLAGS := -latomic

# APP_ABI := armeabi-v7a
APP_ABI := armeabi-v7a arm64-v8a x86
APP_SHORT_COMMANDS := true

APP_PLATFORM := android-23

APP_LDFLAGS += -Wl,-z,max-page-size=16384
APP_LDFLAGS += -Wl,-z,common-page-size=16384

ifeq ($(NDK_DEBUG),1)
  APP_CPPFLAGS += -DCOCOS2D_DEBUG=1
  APP_OPTIM := debug
else
  APP_CPPFLAGS += -DNDEBUG
  APP_OPTIM := release
endif
