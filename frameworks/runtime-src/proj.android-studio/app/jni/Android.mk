LOCAL_PATH := $(call my-dir)

include $(CLEAR_VARS)

LOCAL_MODULE := cocos2dlua

LOCAL_MODULE_FILENAME := libcocos2dlua

LOCAL_SRC_FILES := \
../../../Classes/AppDelegate.cpp \
../../../Classes/lua_sea.cpp \
hellolua/main.cpp \
../../../Classes/lua_bole_util.cpp \
../../../../cocos2d-x/plugin/luabindings/auto/lua_cocos2dx_pluginx_auto.cpp \
../../../../cocos2d-x/plugin/luabindings/manual/lua_pluginx_basic_conversions.cpp \
../../../../cocos2d-x/plugin/luabindings/manual/lua_pluginx_manual_callback.cpp \
../../../../cocos2d-x/plugin/luabindings/manual/lua_pluginx_manual_protocols.cpp \
../../../../cocos2d-x/cocos/scripting/lua-bindings/manual/platform/android/jni/Java_org_cocos2dx_lib_Cocos2dxLuaJavaBridge.cpp

LOCAL_C_INCLUDES := $(LOCAL_PATH)/../../../Classes \
					$(LOCAL_PATH)/../../../../cocos2d-x/plugin/luabindings/auto \
					$(LOCAL_PATH)/../../../../cocos2d-x/plugin/luabindings/manual
LOCAL_WHOLE_STATIC_LIBRARIES += PluginProtocolStatic
# _COCOS_HEADER_ANDROID_BEGIN
# _COCOS_HEADER_ANDROID_END

LOCAL_STATIC_LIBRARIES := cocos2d_lua_static

# _COCOS_LIB_ANDROID_BEGIN
# _COCOS_LIB_ANDROID_END

include $(BUILD_SHARED_LIBRARY)

$(call import-module,scripting/lua-bindings/proj.android)
$(call import-module,../plugin/protocols/proj.android/jni)
#$(call import-module,../plugin/protocols/comonprotocol/jni)

# _COCOS_LIB_IMPORT_ANDROID_BEGIN
# _COCOS_LIB_IMPORT_ANDROID_END
