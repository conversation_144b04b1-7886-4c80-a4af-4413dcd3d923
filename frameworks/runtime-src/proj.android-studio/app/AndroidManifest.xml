<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.grandegames.slots.dafu.casino">

    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        tools:node="remove" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        tools:node="remove" />
    <uses-permission
        android:name="android.permission.READ_PHONE_STATE"
        tools:node="remove" />
    <uses-permission
        android:name="android.permission.ACCESS_COARSE_LOCATION"
        tools:node="remove" />
    <uses-sdk android:targetSdkVersion="28" />
    <uses-feature android:glEsVersion="0x00020000" />
    <supports-screens android:anyDensity="true"
        android:normalScreens="true"
        android:largeScreens="true"
        android:xlargeScreens="true"/>

    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.GET_ACCOUNTS" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED"/>

<!--    <uses-permission android:name="${debug_need_permission}" />-->
<!--    <uses-permission android:name="${debug_need_permission2}" />-->

    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
    <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" />
	<uses-permission android:name="com.android.vending.BILLING" />
    <uses-permission android:name="com.google.android.gms.permission.AD_ID"/>

    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>

    <queries>
        <package android:name="com.facebook.katana" /> <!-- facebook -->
        <package android:name="com.facebook.lite" /> <!-- facebook lite -->
        <package android:name="com.facebook.orca" /> <!-- messenger -->
        <package android:name="com.facebook.mlite" /> <!-- messenger lite -->
        <package android:name="com.twitter.android" /> <!-- twitter -->
        <package android:name="com.instagram.android" /> <!-- instagram -->
        <package android:name="jp.naver.line.android" /> <!-- line -->
        <package android:name="com.snapchat.android" /> <!-- snapchat -->
        <package android:name="org.telegram.messenger" /> <!-- telegram -->
        <package android:name="com.whatsapp" /> <!-- whatsapp -->
        <package android:name="com.whatsapp.w4b" /> <!-- whatsapp for business -->
        <package android:name="com.tencent.mm" /> <!-- wechat -->
        <package android:name="com.google.android.youtube" /> <!-- youtube -->
        <package android:name="com.zhiliaoapp.musically" /> <!-- tiktok -->
        <package android:name="com.ss.android.ugc.aweme" /> <!-- 抖音 -->
        <package android:name="com.kakao.talk" /> <!-- kakaotalk -->
        <package android:name="tv.twitch.android.app" /> <!-- twitch -->
    </queries>

    <application
        android:name="org.cocos2dx.lua.BoleApplication"
        android:allowBackup="true"
        android:icon="@drawable/icon"
        android:hardwareAccelerated="true"
        android:label="@string/app_name"
        android:roundIcon="@drawable/icon"
        android:supportsRtl="true"
        android:usesCleartextTraffic="true"
        android:theme="@style/Theme.FullScreen"
        android:extractNativeLibs="true">
        <!-- Tell Cocos2dxActivity the name of our .so -->
        <meta-data android:name="android.app.lib_name"
            android:value="cocos2dlua" />

        <meta-data android:name="android.max_aspect" android:value="2.4" />
        <!-- <meta-data android:name="android.notch_support" android:value="true"/> -->
        <!-- <meta-data android:name="notch.config" android:value="portrait|landscape"/> -->
        <meta-data android:name="com.facebook.sdk.ApplicationId" android:value="@string/facebook_app_id" />
        <meta-data android:name="com.facebook.sdk.ClientToken" android:value="@string/facebook_client_token"/>
<!--        <meta-data android:name="applovin.sdk.key"-->
<!--            android:value="y5mtl6bhpE2bOwQVb-oqsfT2VVIU0ItyNcq7kPpWdr71mXXLVJ9MzhPTBhyB-q8LQyS8MGf-5n0rJXAqrJBp_a" />-->

        <activity android:name="com.facebook.FacebookActivity"
            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
            android:label="@string/app_name" />
        <provider android:authorities="com.facebook.app.FacebookContentProvider268692577001613"
            android:name="com.facebook.FacebookContentProvider"
            android:exported="true" />
        <activity android:name="org.cocos2dx.lua.AppActivity"
            android:label="@string/app_name"
            android:exported="true"
            android:launchMode="singleInstance"
            android:screenOrientation="sensorLandscape"
            android:theme="@android:style/Theme.NoTitleBar.Fullscreen"
            android:configChanges="orientation|keyboardHidden|screenSize">
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <data android:scheme="${deeplink_scheme}" />
                <data android:scheme="twitterkit-k7quphwy66ahvpgzv7bcaw5zo" />
            </intent-filter>
            <intent-filter>
                <action android:name="${mainactivity_action}" />
                <category android:name="${mainactivity_category}" />
            </intent-filter>
        </activity>
        <activity-alias
            android:name="org.cocos2dx.lua.AppActivityAlias"
            android:enabled="false"
            android:icon="@drawable/icon_vvip"
            android:label="@string/app_name"
            android:exported="true"
            android:launchMode="singleInstance"
            android:screenOrientation="sensorLandscape"
            android:theme="@android:style/Theme.NoTitleBar.Fullscreen"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:targetActivity="org.cocos2dx.lua.AppActivity">
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <data android:scheme="${deeplink_scheme}" />
                <data android:scheme="twitterkit-k7quphwy66ahvpgzv7bcaw5zo" />
            </intent-filter>
            <intent-filter>
                <action android:name="${mainactivity_action}" />
                <category android:name="${mainactivity_category}" />
            </intent-filter>
        </activity-alias>
        <activity android:name="org.cocos2dx.lua.SplashActivity"
            android:exported="true"
            android:theme="@style/Theme.FullScreen"
            android:label="@string/app_name"
            android:screenOrientation="sensorLandscape">
            <intent-filter>
                <action android:name="${splashactivity_action}" />
                <category android:name="${splashactivity_category}" />
            </intent-filter>
        </activity>

        <activity-alias android:name="org.cocos2dx.lua.SplashActivityAlias"
            android:exported="true"
            android:icon="@drawable/icon_vvip"
            android:enabled="false"
            android:theme="@style/Theme.FullScreen"
            android:label="@string/app_name"
            android:screenOrientation="sensorLandscape"
            android:targetActivity="org.cocos2dx.lua.SplashActivity">
            <intent-filter>
                <action android:name="${splashactivity_action}" />
                <category android:name="${splashactivity_category}" />
            </intent-filter>
        </activity-alias>

        <!-- [START firebase_service] -->
        <service
            android:name="org.cocos2dx.fcm.BoleFirebaseMessagingService"
            android:stopWithTask="false"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT"/>
                <action android:name="com.google.firebase.INSTANCE_ID_EVENT"/>
            </intent-filter>
        </service>
        <!-- [END firebase_service] -->

        <receiver
            android:name="org.cocos2dx.bole.ReferrerReceiver"
            android:permission="android.permission.INSTALL_PACKAGES"
            android:exported="true">
            <intent-filter>
                <action android:name="com.android.vending.INSTALL_REFERRER" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </receiver>

        <receiver
            android:name="org.cocos2dx.bole.notification.BootReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <category android:name="android.intent.category.HOME" />
            </intent-filter>
        </receiver>
        <receiver android:name="org.cocos2dx.bole.notification.AlarmReceiver" />

    </application>

</manifest>
